import { Component, inject, output } from '@angular/core'
import {
    <PERSON><PERSON><PERSON>er,
    FormGroup,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms'
import { MatIconButton } from '@angular/material/button'
import { MatCheckbox } from '@angular/material/checkbox'
import { MatIcon } from '@angular/material/icon'
import { MatProgressSpinner } from '@angular/material/progress-spinner'

import { TenderCreateDTO } from '@app/api/model/tenderCreateDTO'
import { InputFieldNumberComponent } from '@app/shared/components/input-field-number/input-field-number.component'
import { InputFieldTextComponent } from '@app/shared/components/input-field-text/input-field-text.component'
import { TenderFileLoadComponent } from '@modules/tenders/components/tender-file/tender-file-load.component'
import { TenderCreateFormControlModel } from '@modules/tenders/models/tender.model'
import { buildTenderCreateForm } from '@modules/tenders/models/tender-form.factory'
import {
    TenderCreateSimpleDTO,
    TenderService,
} from '@modules/tenders/services/tender.service'
import { ButtonComponent } from '@shared/components/button/button.component'
import { DatetimepickerFieldComponent } from '@shared/components/datetimepicker-field/datetimepicker-field.component'
import { InputTextareaComponent } from '@shared/components/input-textarea/input-textarea.component'
import { ModalComponent } from '@shared/components/modal/modal.component'
import { SeparatorComponent } from '@shared/components/separator/separator.component'
import { DialogService } from '@shared/services/dialog.service'
import { PopUpService } from '@shared/services/pop-up.service'
import { validUrlValidator } from '@shared/utils/custom-form-validation/custom-validators'

import { TranslateModule, TranslateService } from '@ngx-translate/core'
import { BehaviorSubject, finalize } from 'rxjs'

@Component({
    selector: 'app-tender-modal-create',
    imports: [
        ReactiveFormsModule,
        InputFieldTextComponent,
        InputTextareaComponent,
        SeparatorComponent,
        ModalComponent,
        TranslateModule,
        DatetimepickerFieldComponent,
        MatProgressSpinner,
        TenderFileLoadComponent,
        InputFieldNumberComponent,
        MatCheckbox,
        MatIcon,
        MatIconButton,
        ButtonComponent,
    ],
    templateUrl: './tender-modal-create.component.html',
    standalone: true,
    styleUrl: './tender-modal-create.component.scss',
})
export class TenderModalCreateComponent {
    private readonly translate = inject(TranslateService)

    loading$ = new BehaviorSubject<boolean>(false)
    closed = output()

    mode: 'ai-supported' | 'manual' = 'ai-supported'

    manualForm: FormGroup<TenderCreateFormControlModel>
    aiSupportedForm!: FormGroup

    constructor(
        private fb: FormBuilder,
        private tenderService: TenderService,
        private popUpService: PopUpService,
        private confirmDialogService: DialogService
    ) {
        this.manualForm = buildTenderCreateForm(fb)
        this.aiSupportedForm = fb.group({
            title: ['', Validators.required],
            sourceUrl: ['', validUrlValidator()], // if sourceUrl is given it should be a valid URL
            description: ['', Validators.required],
            files: [[]],
        })
    }

    get tenderForm(): FormGroup {
        return this.mode === 'ai-supported'
            ? this.aiSupportedForm
            : this.manualForm
    }

    onSave() {
        const currentForm = this.tenderForm

        if (currentForm.valid) {
            const formValue = currentForm.getRawValue()
            const files: Blob[] = (formValue.files?.newFiles ?? []).map(
                (item: any) => item.file
            )

            if (this.mode === 'manual') {
                const newTender: TenderCreateDTO = {
                    ...formValue,
                    contractValue: formValue.contractValue ?? undefined,
                    maximumBudget: formValue.maximumBudget ?? undefined,
                    comment: formValue.comment ?? undefined,
                    rating: formValue.rating ?? undefined,
                    isFavorite: formValue.isFavorite,
                    files: files,
                }

                this.loading$.next(true)

                this.tenderService
                    .createTender(newTender)
                    .pipe(finalize(() => this.loading$.next(false)))
                    .subscribe({
                        next: () => {
                            const successMessage = this.translate.instant(
                                'popup.success.create-tender'
                            )
                            this.popUpService.openSuccessPopUp(
                                successMessage,
                                'popup.success.message'
                            )

                            this.manualForm.markAsPristine()
                            void this.onClose()
                        },
                        error: () => {
                            const errorMessage = this.translate.instant(
                                'popup.error.create-tender'
                            )
                            this.popUpService.openErrorPopUp(
                                errorMessage,
                                'popup.error.message'
                            )
                        },
                    })
            } else if (this.mode === 'ai-supported') {
                const newTender: TenderCreateSimpleDTO = {
                    title: formValue.title,
                    sourceUrl: formValue.sourceUrl,
                    description: formValue.description,
                    files: files,
                }

                this.loading$.next(true)

                this.tenderService
                    .createTenderWithAI(newTender)
                    .pipe(finalize(() => this.loading$.next(false)))
                    .subscribe({
                        next: () => {
                            const successMessage = this.translate.instant(
                                'popup.success.create-tender'
                            )
                            this.popUpService.openSuccessPopUp(
                                successMessage,
                                'popup.success.message'
                            )
                            this.aiSupportedForm.markAsPristine()
                            void this.onClose()
                        },
                        error: () => {
                            const errorMessage = this.translate.instant(
                                'popup.error.create-tender'
                            )
                            this.popUpService.openErrorPopUp(
                                errorMessage,
                                'popup.error.message'
                            )
                        },
                    })
            }
        }
    }

    switchToAiSupported() {
        this.mode = 'ai-supported'
    }
    switchToManual() {
        this.mode = 'manual'
    }

    async onClose() {
        if (this.manualForm.dirty)
            if (!(await this.confirmDialogService.openConfirmDialog())) return

        this.closed.emit()
    }
}
